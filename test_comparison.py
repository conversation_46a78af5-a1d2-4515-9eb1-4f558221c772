# -*- coding: utf-8 -*-
"""
测试对比图片生成
"""
import pandas as pd
import matplotlib.pyplot as plt
import os

# 读取已保存的对齐数据
aligned_data = pd.read_csv('aligned_data.csv', index_col=0, parse_dates=True)

# 创建两个版本的BAM1020数据用于对比
# 模拟原始时间戳（向后推1小时）
aligned_data_original = aligned_data.copy()
aligned_data_original.index = aligned_data_original.index + pd.Timedelta(hours=1)

# 调整版本保持原样
aligned_data_adjusted = aligned_data.copy()

# 过滤出 2023 年 11 月到 12 月的数据
filtered_data_original = aligned_data_original['2023-11':'2023-12']
filtered_data_adjusted = aligned_data_adjusted['2023-11':'2023-12']

print(f"原始数据天数: {len(filtered_data_original.index.normalize().unique())}")
print(f"调整后数据天数: {len(filtered_data_adjusted.index.normalize().unique())}")

# 创建保存图像的文件夹
output_folder_original = '202311-202312_原始时间戳'
output_folder_adjusted = '202311-202312_调整后时间戳'

if not os.path.exists(output_folder_original):
    os.makedirs(output_folder_original)
    print(f"创建文件夹: {output_folder_original}")
if not os.path.exists(output_folder_adjusted):
    os.makedirs(output_folder_adjusted)
    print(f"创建文件夹: {output_folder_adjusted}")

# 获取这段时间内的所有天数
unique_days_original = filtered_data_original.index.normalize().unique()
unique_days_adjusted = filtered_data_adjusted.index.normalize().unique()

print(f"开始生成原始时间戳图片，共{len(unique_days_original)}天")

# 生成原始时间戳的图片（只生成前3天作为测试）
for i, day in enumerate(unique_days_original[:3]):
    daily_data = filtered_data_original[filtered_data_original.index.normalize() == day]

    # 绘制 PM2.5 时间序列图
    plt.figure(figsize=(14, 7))
    plt.plot(daily_data.index, daily_data['pm2.5'], label='Grimm180 PM2.5')
    plt.plot(daily_data.index, daily_data['PM2.5_bam'], label='BAM1020 PM2.5 (原始时间戳)')
    plt.plot(daily_data.index, daily_data['PM2.5_dxc'], label='DXC1 PM2.5')
    plt.xlabel('Time')
    plt.ylabel('PM2.5 Concentration (µg/m³)')
    plt.title(f'Time Series of PM2.5 Concentration on {day.date()} (原始时间戳)')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(output_folder_original, f'PM2.5_{day.date()}.png'))
    plt.close()
    print(f"已生成原始时间戳图片: PM2.5_{day.date()}.png")

print(f"开始生成调整后时间戳图片，共{len(unique_days_adjusted)}天")

# 生成调整后时间戳的图片（只生成前3天作为测试）
for i, day in enumerate(unique_days_adjusted[:3]):
    daily_data = filtered_data_adjusted[filtered_data_adjusted.index.normalize() == day]

    # 绘制 PM2.5 时间序列图
    plt.figure(figsize=(14, 7))
    plt.plot(daily_data.index, daily_data['pm2.5'], label='Grimm180 PM2.5')
    plt.plot(daily_data.index, daily_data['PM2.5_bam'], label='BAM1020 PM2.5 (调整后时间戳)')
    plt.plot(daily_data.index, daily_data['PM2.5_dxc'], label='DXC1 PM2.5')
    plt.xlabel('Time')
    plt.ylabel('PM2.5 Concentration (µg/m³)')
    plt.title(f'Time Series of PM2.5 Concentration on {day.date()} (调整后时间戳)')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(output_folder_adjusted, f'PM2.5_{day.date()}.png'))
    plt.close()
    print(f"已生成调整后时间戳图片: PM2.5_{day.date()}.png")

print(f"原始时间戳图片保存在: {output_folder_original}")
print(f"调整后时间戳图片保存在: {output_folder_adjusted}")
print("对比完成！您可以比较两个文件夹中的同名图片来查看BAM1020时间戳调整的效果。") 