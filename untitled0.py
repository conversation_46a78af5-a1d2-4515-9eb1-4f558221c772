# -*- coding: utf-8 -*-
"""
Created on Mon Aug 12 17:27:39 2024

@author: 89241
"""
import pandas as pd
import chardet
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np
import os

# 自动检测 CSV 文件的编码
with open('C:\\Users\\<USER>\\Desktop\\pm2.5\\grimm180.csv', 'rb') as f:
    result = chardet.detect(f.read())
file_encoding = result['encoding']

# 使用检测到的编码读取 grimm180.csv 文件
grimm180_data = pd.read_csv('C:\\Users\\<USER>\\Desktop\\pm2.5\\grimm180.csv', encoding=file_encoding, low_memory=False)

# 将时间列设置为 datetime 对象并设定为 UTC 时间
grimm180_data['time'] = pd.to_datetime(grimm180_data['time'], utc=True)

# 将时间戳从 UTC 转换为本地时间（例如 Asia/Shanghai）
grimm180_data['time'] = grimm180_data['time'].dt.tz_convert('Asia/Shanghai')
# 如果你希望删除时区信息，只保留本地时间戳
grimm180_data['time'] = grimm180_data['time'].dt.tz_localize(None)

# 重新设置时间列为索引
grimm180_data.set_index('time', inplace=True)

# 检查转换后的数据
print(grimm180_data.head())

# 直接读取 bam-1020.xls 文件，无需检测编码和使用 low_memory 参数
bam1020_data = pd.read_excel('C:\\Users\\<USER>\\Desktop\\pm2.5\\bam-1020.xls')

# 将时间列转换为 datetime 对象
bam1020_data['time'] = pd.to_datetime(bam1020_data['time'])

# 创建两个版本的BAM1020数据：原始版本和调整版本
bam1020_data_original = bam1020_data.copy()
bam1020_data_adjusted = bam1020_data.copy()

# 将BAM1020数据的时间戳向前平移1小时（仅调整版本）
bam1020_data_adjusted['time'] = bam1020_data_adjusted['time'] - pd.Timedelta(hours=1)

# 自动检测 dxc1.csv 文件的编码
with open('C:\\Users\\<USER>\\Desktop\\pm2.5\\dxc1.csv', 'rb') as f:
    result = chardet.detect(f.read())
file_encoding = result['encoding']

# 使用检测到的编码读取 dxc1.csv 文件
dxc1_data = pd.read_csv('C:\\Users\\<USER>\\Desktop\\pm2.5\\dxc1.csv', encoding=file_encoding, low_memory=False)

# 将时间列转换为 datetime 对象
dxc1_data['time'] = pd.to_datetime(dxc1_data['time'])

# 删除重复行（如果存在）
grimm180_data.drop_duplicates(inplace=True)
bam1020_data_original.drop_duplicates(inplace=True)
bam1020_data_adjusted.drop_duplicates(inplace=True)
dxc1_data.drop_duplicates(inplace=True)

# 统一时间戳为小时精度（或根据需要）
grimm180_data['time'] = grimm180_data.index.floor('h')
bam1020_data_original['time'] = bam1020_data_original['time'].dt.floor('h')
bam1020_data_adjusted['time'] = bam1020_data_adjusted['time'].dt.floor('h')
dxc1_data['time'] = dxc1_data['time'].dt.floor('h')

# 转换数据列为数值类型，确保可以进行聚合
dxc1_data['PM2.5'] = pd.to_numeric(dxc1_data['PM2.5'], errors='coerce')
dxc1_data['PM10'] = pd.to_numeric(dxc1_data['PM10'], errors='coerce')

# 重置索引，避免索引和列名冲突
grimm180_data = grimm180_data.reset_index(drop=True)
bam1020_data_original = bam1020_data_original.reset_index(drop=True)
bam1020_data_adjusted = bam1020_data_adjusted.reset_index(drop=True)
dxc1_data = dxc1_data.reset_index(drop=True)

# 对每个数据集进行聚合，确保每个时间戳只有一条记录
grimm180_data = grimm180_data.groupby('time').mean()
bam1020_data_original = bam1020_data_original.groupby('time').mean()
bam1020_data_adjusted = bam1020_data_adjusted.groupby('time').mean()
dxc1_data = dxc1_data.groupby('time').mean()

# 重新设置时间列为索引后对齐 - 原始版本
aligned_data_original = grimm180_data.join(bam1020_data_original, lsuffix='_grimm', rsuffix='_bam')
aligned_data_original = aligned_data_original.join(dxc1_data, lsuffix='_bam', rsuffix='_dxc')

# 重新设置时间列为索引后对齐 - 调整版本
aligned_data_adjusted = grimm180_data.join(bam1020_data_adjusted, lsuffix='_grimm', rsuffix='_bam')
aligned_data_adjusted = aligned_data_adjusted.join(dxc1_data, lsuffix='_bam', rsuffix='_dxc')

# 处理缺失值（如填补或删除）
aligned_data_original.dropna(inplace=True)
aligned_data_adjusted.dropna(inplace=True)

# 为了兼容性，保留原来的aligned_data变量
aligned_data = aligned_data_adjusted

# 检查最终结果
print(aligned_data.head())

# 保存对齐后的数据
aligned_data.to_csv('C:\\Users\\<USER>\\Desktop\\pm2.5\\aligned_data.csv')

# 输出描述性统计
print(aligned_data.describe())

# 移除相关性分析部分
# correlation_matrix = aligned_data.corr()
# print(correlation_matrix)

# 移除相关性矩阵可视化
# plt.figure(figsize=(12, 5))
# plt.subplot(1, 2, 1)
# pm25_cols = ['pm2.5', 'PM2.5_bam', 'PM2.5_dxc']
# pm25_corr = aligned_data[pm25_cols].corr()
# sns.heatmap(pm25_corr, annot=True, cmap='coolwarm', fmt=".2f", linewidths=0.5)
# plt.title('PM2.5 Correlation Matrix', fontsize=14)

# plt.subplot(1, 2, 2)
# pm10_cols = ['pm10', 'PM10_bam', 'PM10_dxc']
# pm10_corr = aligned_data[pm10_cols].corr()
# sns.heatmap(pm10_corr, annot=True, cmap='coolwarm', fmt=".2f", linewidths=0.5)
# plt.title('PM10 Correlation Matrix', fontsize=14)

# plt.tight_layout()
# plt.show()

# 移除相关性输出
# pm25_correlation = aligned_data[['pm2.5', 'PM2.5_bam', 'PM2.5_dxc']].corr()
# pm10_correlation = aligned_data[['pm10', 'PM10_bam', 'PM10_dxc']].corr()
# print("PM2.5 相关性矩阵:")
# print(pm25_correlation)
# print("\nPM10 相关性矩阵:")
# print(pm10_correlation)

# 分别绘制PM2.5和PM10的时间序列图
plt.figure(figsize=(16, 6))

# PM2.5时间序列图
plt.subplot(1, 2, 1)
plt.plot(aligned_data.index, aligned_data['pm2.5'], label='Grimm180 PM2.5')
plt.plot(aligned_data.index, aligned_data['PM2.5_bam'], label='BAM1020 PM2.5')
plt.plot(aligned_data.index, aligned_data['PM2.5_dxc'], label='DXC1 PM2.5')
plt.xlabel('Time')
plt.ylabel('PM2.5 Concentration (µg/m³)')
plt.title('PM2.5 Time Series Across Devices')
plt.legend()
plt.grid(True)

# PM10时间序列图
plt.subplot(1, 2, 2)
plt.plot(aligned_data.index, aligned_data['pm10'], label='Grimm180 PM10')
plt.plot(aligned_data.index, aligned_data['PM10_bam'], label='BAM1020 PM10')
plt.plot(aligned_data.index, aligned_data['PM10_dxc'], label='DXC1 PM10')
plt.xlabel('Time')
plt.ylabel('PM10 Concentration (µg/m³)')
plt.title('PM10 Time Series Across Devices')
plt.legend()
plt.grid(True)

plt.tight_layout()
plt.show()

# 过滤出 2023 年 11 月到 12 月的数据
filtered_data_original = aligned_data_original['2023-11':'2023-12']
filtered_data_adjusted = aligned_data_adjusted['2023-11':'2023-12']

print(f"原始数据天数: {len(filtered_data_original.index.normalize().unique())}")
print(f"调整后数据天数: {len(filtered_data_adjusted.index.normalize().unique())}")

# 创建保存图像的文件夹
output_folder_original = '20250803/原始时间戳'
output_folder_adjusted = '20250803/调整后时间戳'

if not os.path.exists('20250803'):
    os.makedirs('20250803')
    print("创建主文件夹: 20250803")

if not os.path.exists(output_folder_original):
    os.makedirs(output_folder_original)
    print(f"创建文件夹: {output_folder_original}")
if not os.path.exists(output_folder_adjusted):
    os.makedirs(output_folder_adjusted)
    print(f"创建文件夹: {output_folder_adjusted}")

# 获取这段时间内的所有天数
unique_days_original = filtered_data_original.index.normalize().unique()
unique_days_adjusted = filtered_data_adjusted.index.normalize().unique()

print(f"开始生成原始时间戳图片，共{len(unique_days_original)}天")

# 生成原始时间戳的图片
for day in unique_days_original:
    daily_data = filtered_data_original[filtered_data_original.index.normalize() == day]

    # 绘制 PM2.5 时间序列图
    plt.figure(figsize=(14, 7))
    plt.plot(daily_data.index, daily_data['pm2.5'], label='Grimm180 PM2.5')
    plt.plot(daily_data.index, daily_data['PM2.5_bam'], label='BAM1020 PM2.5 (原始时间戳)')
    plt.plot(daily_data.index, daily_data['PM2.5_dxc'], label='DXC1 PM2.5')
    plt.xlabel('Time')
    plt.ylabel('PM2.5 Concentration (µg/m³)')
    plt.title(f'Time Series of PM2.5 Concentration on {day.date()} (原始时间戳)')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(output_folder_original, f'PM2.5_{day.date()}.png'))
    plt.close()

    # 绘制 PM10 时间序列图
    plt.figure(figsize=(14, 7))
    plt.plot(daily_data.index, daily_data['pm10'], label='Grimm180 PM10')
    plt.plot(daily_data.index, daily_data['PM10_bam'], label='BAM1020 PM10 (原始时间戳)')
    plt.plot(daily_data.index, daily_data['PM10_dxc'], label='DXC1 PM10')
    plt.xlabel('Time')
    plt.ylabel('PM10 Concentration (µg/m³)')
    plt.title(f'Time Series of PM10 Concentration on {day.date()} (原始时间戳)')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(output_folder_original, f'PM10_{day.date()}.png'))
    plt.close()

# 生成调整后时间戳的图片
for day in unique_days_adjusted:
    daily_data = filtered_data_adjusted[filtered_data_adjusted.index.normalize() == day]

    # 绘制 PM2.5 时间序列图
    plt.figure(figsize=(14, 7))
    plt.plot(daily_data.index, daily_data['pm2.5'], label='Grimm180 PM2.5')
    plt.plot(daily_data.index, daily_data['PM2.5_bam'], label='BAM1020 PM2.5 (调整后时间戳)')
    plt.plot(daily_data.index, daily_data['PM2.5_dxc'], label='DXC1 PM2.5')
    plt.xlabel('Time')
    plt.ylabel('PM2.5 Concentration (µg/m³)')
    plt.title(f'Time Series of PM2.5 Concentration on {day.date()} (调整后时间戳)')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(output_folder_adjusted, f'PM2.5_{day.date()}.png'))
    plt.close()

    # 绘制 PM10 时间序列图
    plt.figure(figsize=(14, 7))
    plt.plot(daily_data.index, daily_data['pm10'], label='Grimm180 PM10')
    plt.plot(daily_data.index, daily_data['PM10_bam'], label='BAM1020 PM10 (调整后时间戳)')
    plt.plot(daily_data.index, daily_data['PM10_dxc'], label='DXC1 PM10')
    plt.xlabel('Time')
    plt.ylabel('PM10 Concentration (µg/m³)')
    plt.title(f'Time Series of PM10 Concentration on {day.date()} (调整后时间戳)')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(output_folder_adjusted, f'PM10_{day.date()}.png'))
    plt.close()

print(f"原始时间戳图片保存在: {output_folder_original}")
print(f"调整后时间戳图片保存在: {output_folder_adjusted}")
print("对比完成！您可以比较两个文件夹中的同名图片来查看BAM1020时间戳调整的效果。")

# 完整的质量控制系统
class OptimizedDataQC:
    def __init__(self, window_size=24):
        self.window_size = window_size
        self.qc_results = {}
        
    def basic_physical_check(self, data, instrument_name):
        """第一层：基础物理检查"""
        print(f"对{instrument_name}进行基础物理检查...")
        
        qc_flags = pd.DataFrame(index=data.index)
        qc_flags['valid_pm25'] = True
        qc_flags['valid_pm10'] = True
        qc_flags['valid_ratio'] = True
        
        # 自动识别PM2.5和PM10列
        pm25_col = None
        pm10_col = None
        
        for col in data.columns:
            col_lower = str(col).lower()
            if any(x in col_lower for x in ['pm2.5', 'pm25']) or 'PM2.5' in str(col):
                pm25_col = col
            if any(x in col_lower for x in ['pm10', 'pm_10']) or 'PM10' in str(col):
                pm10_col = col
        
        print(f"  识别到列: PM2.5={pm25_col}, PM10={pm10_col}")
        
        # 数值范围检查
        if pm25_col is not None:
            qc_flags['valid_pm25'] = (data[pm25_col] >= 0) & (data[pm25_col] <= 500)
            invalid_count = (~qc_flags['valid_pm25']).sum()
            print(f"  PM2.5范围检查: {invalid_count} 条无效 ({invalid_count/len(data)*100:.1f}%)")
            
        if pm10_col is not None:
            qc_flags['valid_pm10'] = (data[pm10_col] >= 0) & (data[pm10_col] <= 1000)
            invalid_count = (~qc_flags['valid_pm10']).sum()
            print(f"  PM10范围检查: {invalid_count} 条无效 ({invalid_count/len(data)*100:.1f}%)")
            
        # PM2.5与PM10关系检查
        if pm25_col is not None and pm10_col is not None:
            valid_both = data[pm25_col].notna() & data[pm10_col].notna()
            qc_flags['valid_ratio'] = ~valid_both | (data[pm25_col] <= data[pm10_col])
            invalid_ratio = (~qc_flags['valid_ratio']).sum()
            print(f"  比值检查: {invalid_ratio} 条PM2.5>PM10 ({invalid_ratio/len(data)*100:.1f}%)")
            
        return qc_flags, pm25_col, pm10_col
    
    def n1_outlier_detection(self, data, column):
        """N1异常值检验法"""
        if column not in data.columns:
            return pd.Series(False, index=data.index)
            
        valid_data = data[column].dropna()
        if len(valid_data) < 10:
            return pd.Series(False, index=data.index)
            
        outlier_flags = pd.Series(False, index=data.index)
        
        # 转换为数值索引进行处理
        valid_values = valid_data.values
        valid_indices = valid_data.index
        
        for i in range(len(valid_values)):
            # 24小时窗口
            start_idx = max(0, i - self.window_size//2)
            end_idx = min(len(valid_values), i + self.window_size//2 + 1)
            
            window_values = valid_values[start_idx:end_idx]
            
            if len(window_values) >= 5:
                # 使用稳健统计量
                window_median = np.median(window_values)
                window_mad = np.median(np.abs(window_values - window_median))
                
                if window_mad > 0:
                    # 修正的Z分数
                    modified_z = 0.6745 * (valid_values[i] - window_median) / window_mad
                    
                    # 动态阈值
                    if window_median < 35:  # 清洁条件
                        threshold = 2.5
                    elif window_median > 75:  # 污染条件
                        threshold = 3.5
                    else:
                        threshold = 3.0
                        
                    if abs(modified_z) > threshold:
                        outlier_flags[valid_indices[i]] = True
                        
        return outlier_flags
    
    def pollution_event_verification(self, data_dict, outlier_flags, pollutant_type):
        """重污染事件验证"""
        verified_outliers = outlier_flags.copy()
        outlier_indices = outlier_flags[outlier_flags].index
        
        for idx in outlier_indices:
            # 检查前后2小时窗口
            window_start = idx - pd.Timedelta(hours=2)
            window_end = idx + pd.Timedelta(hours=2)
            
            high_value_count = 0
            total_instruments = 0
            
            for instrument, data in data_dict.items():
                if idx in data.index:
                    # 查找对应的污染物列
                    target_cols = []
                    for col in data.columns:
                        col_str = str(col).lower()
                        if pollutant_type == 'pm2.5' and any(x in col_str for x in ['pm2.5', 'pm25']):
                            target_cols.append(col)
                        elif pollutant_type == 'pm10' and any(x in col_str for x in ['pm10', 'pm_10']):
                            target_cols.append(col)
                    
                    if target_cols:
                        col = target_cols[0]
                        try:
                            window_data = data[col].loc[window_start:window_end].dropna()
                            if len(window_data) >= 2:
                                total_instruments += 1
                                # 检查是否有持续高值
                                high_threshold = window_data.quantile(0.7)
                                if (window_data > high_threshold).sum() >= 2:
                                    high_value_count += 1
                        except:
                            continue
            
            # 如果多个仪器都显示高值，可能是真实污染事件
            if total_instruments >= 2 and high_value_count >= 2:
                verified_outliers[idx] = False
                
        return verified_outliers

# 执行质量控制
print("开始运行24小时窗口质量控制系统...")
print("="*60)

# 准备数据字典 - 修复变量名问题
data_sources = {}

# 检查并添加可用的数据源
try:
    # 从aligned_data中提取各仪器数据
    if 'aligned_data' in locals():
        # Grimm180数据 (pm2.5, pm10列)
        grimm_cols = ['pm2.5', 'pm10']
        available_grimm_cols = [col for col in grimm_cols if col in aligned_data.columns]
        if available_grimm_cols:
            grimm_data = aligned_data[available_grimm_cols].copy()
            data_sources['Grimm180'] = grimm_data
            print(f"Grimm180数据加载成功: {len(grimm_data)}条记录")
        
        # BAM1020数据 (带_bam后缀的列)
        bam_cols = [col for col in aligned_data.columns if '_bam' in col]
        if bam_cols:
            bam_data = aligned_data[bam_cols].copy()
            data_sources['BAM1020'] = bam_data
            print(f"BAM1020数据加载成功: {len(bam_data)}条记录")
        
        # DXC1数据 (带_dxc后缀的列)
        dxc_cols = [col for col in aligned_data.columns if '_dxc' in col]
        if dxc_cols:
            dxc_data = aligned_data[dxc_cols].copy()
            data_sources['DXC1'] = dxc_data
            print(f"DXC1数据加载成功: {len(dxc_data)}条记录")
    
    # 如果没有aligned_data，尝试使用原始数据变量
    else:
        print("未找到aligned_data，尝试使用原始数据变量...")
        # 这里可以添加对原始数据变量的检查
        
except Exception as e:
    print(f"数据加载出错: {e}")

print(f"\n总共加载了{len(data_sources)}个数据源: {list(data_sources.keys())}")

# 显示数据概况
print("\n原始数据概况:")
for name, data in data_sources.items():
    print(f"\n{name}:")
    print(f"  时间范围: {data.index.min()} 到 {data.index.max()}")
    print(f"  数据点数: {len(data)}")
    print(f"  列名: {list(data.columns)}")

# 开始质控处理
print("\n" + "="*60)
print("开始质量控制处理...")

qc_system = OptimizedDataQC(window_size=24)
cleaned_data_dict = {}
qc_summary = {}

for instrument, data in data_sources.items():
    print(f"\n{'='*20} {instrument} {'='*20}")
    
    try:
        # 基础物理检查
        physical_flags, pm25_col, pm10_col = qc_system.basic_physical_check(data, instrument)
        
        # 异常值检测
        pm25_outliers = pd.Series(False, index=data.index)
        pm10_outliers = pd.Series(False, index=data.index)
        
        if pm25_col:
            pm25_outliers = qc_system.n1_outlier_detection(data, pm25_col)
            pm25_outliers = qc_system.pollution_event_verification(data_sources, pm25_outliers, 'pm2.5')
            print(f"  PM2.5异常值: {pm25_outliers.sum()} 条 ({pm25_outliers.sum()/len(data)*100:.1f}%)")
        
        if pm10_col:
            pm10_outliers = qc_system.n1_outlier_detection(data, pm10_col)
            pm10_outliers = qc_system.pollution_event_verification(data_sources, pm10_outliers, 'pm10')
            print(f"  PM10异常值: {pm10_outliers.sum()} 条 ({pm10_outliers.sum()/len(data)*100:.1f}%)")
        
        # 应用质控
        cleaned_data = data.copy()
        
        original_pm25 = data[pm25_col].notna().sum() if pm25_col else 0
        original_pm10 = data[pm10_col].notna().sum() if pm10_col else 0
        
        if pm25_col:
            invalid_mask = (~physical_flags['valid_pm25']) | pm25_outliers
            cleaned_data.loc[invalid_mask, pm25_col] = np.nan
            
        if pm10_col:
            invalid_mask = (~physical_flags['valid_pm10']) | pm10_outliers
            cleaned_data.loc[invalid_mask, pm10_col] = np.nan
        
        final_pm25 = cleaned_data[pm25_col].notna().sum() if pm25_col else 0
        final_pm10 = cleaned_data[pm10_col].notna().sum() if pm10_col else 0
        
        cleaned_data_dict[instrument] = cleaned_data
        
        # 统计结果
        qc_summary[instrument] = {
            'pm25_original': original_pm25,
            'pm25_final': final_pm25,
            'pm25_retention': final_pm25/original_pm25*100 if original_pm25 > 0 else 0,
            'pm10_original': original_pm10,
            'pm10_final': final_pm10,
            'pm10_retention': final_pm10/original_pm10*100 if original_pm10 > 0 else 0
        }
        
        print(f"  质控结果:")
        if pm25_col:
            print(f"    PM2.5: {original_pm25} → {final_pm25} (保留率: {final_pm25/original_pm25*100:.1f}%)")
        if pm10_col:
            print(f"    PM10: {original_pm10} → {final_pm10} (保留率: {final_pm10/original_pm10*100:.1f}%)")
            
    except Exception as e:
        print(f"  处理{instrument}时出错: {e}")
        import traceback
        traceback.print_exc()

# 生成总结报告
print("\n" + "="*60)
print("质量控制总结报告")
print("="*60)

for instrument, stats in qc_summary.items():
    print(f"\n{instrument}:")
    print(f"  PM2.5数据保留率: {stats['pm25_retention']:.1f}% ({stats['pm25_final']}/{stats['pm25_original']})")
    print(f"  PM10数据保留率: {stats['pm10_retention']:.1f}% ({stats['pm10_final']}/{stats['pm10_original']})")

print(f"\n质量控制完成！共处理{len(data_sources)}个数据源。")



